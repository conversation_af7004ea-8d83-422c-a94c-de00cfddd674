# 连续负值转正信号股票回测程序

## 程序概述

基于原有的股票回测框架，设计了一个新的交易策略：**连续负值转正信号策略**。该策略通过识别主力资金和超大单资金连续流出后转为流入的信号，捕捉潜在的反弹机会。

## 核心策略逻辑

### 1. 连续负值检测
- **条件**：连续5天或5天以上
- **指标**：'主力净流入-净占比' 和 '超大单净流入-净占比' 都为负值
- **意义**：识别主力资金持续流出的股票

### 2. 转正信号确认
- **条件**：连续负值期结束后的下一天（第N+1天）
- **指标**：'主力净流入-净占比' 和 '超大单净流入-净占比' 都转为正值
- **意义**：确认资金流向发生反转

### 3. 交易执行策略
- **买入时机**：转正信号确认后的第二天（第N+2天）开盘价买入
- **卖出时机**：买入后第二天（第N+3天）最高价卖出
- **持仓周期**：2个交易日

### 4. 收益计算
- **买入价格**：第N+2天开盘价
- **卖出价格**：第N+3天最高价
- **收益率**：(卖出价格 - 买入价格) / 买入价格 × 100%

## 文件说明

### 主程序文件
- **`lianxu_fuzhi_zhuanzheng_huice.py`**：完整的回测程序
- **`test_lianxu_fuzhi_algorithm.py`**：算法逻辑测试程序

### 数据依赖
程序需要以下数据目录：
1. **股票行情数据**：`d:\Andy\Data\MktEqudGet\`
2. **涨跌停数据**：`d:\Andy\Data\MktLimitGet\`
3. **资金流向数据**：`stock_fund_flow_data\`

### 输出文件
- **主输出**：`lianxu_fuzhi_zhuanzheng_huice_YYYYMMDD.csv`
- **备用输出**：`lianxu_fuzhi_zhuanzheng_huice_YYYYMMDD_utf8.csv`

## 输出字段说明

| 字段名 | 说明 |
|--------|------|
| secID | 股票代码 |
| secShortName | 股票名称 |
| 连续负值开始日期 | 连续负值期的开始日期 |
| 连续负值结束日期 | 连续负值期的结束日期 |
| 连续负值天数 | 连续负值的天数（≥5天） |
| 转正确认日期 | 两个指标都转为正值的日期 |
| 买入日期 | 实际买入日期（转正确认后第二天） |
| 卖出日期 | 实际卖出日期（买入后第二天） |
| 买入价格_开盘价 | 买入价格（买入日开盘价） |
| 买入日期_开盘价 | 买入日的开盘价 |
| 买入日期_收盘价 | 买入日的收盘价 |
| 买入日期_最低价 | 买入日的最低价 |
| 买入日期_涨跌幅 | 买入日的涨跌幅 |
| 卖出价格_最高价 | 卖出价格（卖出日最高价） |
| 收益率 | 交易收益率（%） |
| 确认日主力净占比 | 转正确认日的主力净流入占比 |
| 确认日超大单净占比 | 转正确认日的超大单净流入占比 |

## 使用方法

### 1. 环境准备
确保Python环境已安装以下库：
```bash
pip install pandas numpy
```

### 2. 数据准备
确保以下目录存在并包含相应的数据文件：
- 股票行情数据目录
- 涨跌停数据目录  
- 资金流向数据目录

### 3. 运行程序

#### 测试算法逻辑
```bash
python test_lianxu_fuzhi_algorithm.py
```

#### 运行完整回测
```bash
python lianxu_fuzhi_zhuanzheng_huice.py
```

### 4. 查看结果
程序运行完成后会生成：
- CSV结果文件
- 详细的统计报告
- 最佳和最差案例分析

## 统计报告内容

### 基础统计
- 分析日期范围
- 总交易日数
- 找到的交易信号数量

### 收益率分析
- 平均收益率
- 最高/最低收益率
- 盈利/亏损次数
- 胜率统计

### 分布分析
- 连续负值天数分布
- 按月份统计
- 最佳/最差案例展示

## 算法特点

### 优势
1. **明确的信号定义**：基于资金流向的量化指标
2. **风险控制**：短期持仓，快速止盈止损
3. **逻辑清晰**：连续负值转正的反转逻辑
4. **数据完整**：记录详细的交易数据便于分析

### 注意事项
1. **数据依赖**：需要完整的资金流向数据
2. **市场环境**：策略效果可能受市场环境影响
3. **交易成本**：实际交易需考虑手续费等成本
4. **滑点风险**：开盘价买入和最高价卖出可能存在执行偏差

## 参数调整

可以根据需要调整以下参数：
- **连续负值天数阈值**：当前设置为5天，可调整为其他值
- **买入卖出时机**：可调整为其他价格（如收盘价等）
- **持仓周期**：可调整持仓天数
- **过滤条件**：可添加其他股票筛选条件

## 扩展功能

### 可能的改进方向
1. **多因子结合**：结合技术指标、基本面数据
2. **动态阈值**：根据市场波动调整参数
3. **风险管理**：添加止损机制
4. **组合优化**：多股票组合策略

### 回测优化
1. **滑点模拟**：更真实的价格执行模拟
2. **交易成本**：包含手续费、印花税等
3. **资金管理**：固定金额或比例投资
4. **基准比较**：与市场指数对比

## 技术实现

### 核心算法
```python
def detect_consecutive_negative_to_positive_signals(data):
    # 连续负值检测
    # 转正信号确认
    # 交易时机计算
    # 收益率计算
```

### 数据处理
- 复用原框架的数据读取和过滤逻辑
- 资金流向数据的匹配和处理
- 异常数据的处理和容错

### 性能优化
- 按股票分组处理，减少内存占用
- 进度显示，便于监控处理状态
- 异常处理，确保程序稳定运行

## 联系信息

如有问题或建议，请联系开发团队。

---
*程序基于原有股票回测框架开发，保持了数据处理和输出格式的一致性。*
