#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连续负值转正信号算法测试程序
用于验证核心算法逻辑的正确性
作者：AI Assistant
创建时间：2025-07-28
"""

import pandas as pd
import numpy as np

def is_negative_value(value):
    """判断是否为负值"""
    if pd.isna(value) or value == "" or value is None:
        return False
    try:
        return float(value) < 0
    except (ValueError, TypeError):
        return False

def is_positive_value(value):
    """判断是否为正值"""
    if pd.isna(value) or value == "" or value is None:
        return False
    try:
        return float(value) > 0
    except (ValueError, TypeError):
        return False

def detect_consecutive_negative_to_positive_signals(data):
    """
    检测连续负值转正信号
    
    参数:
    data: DataFrame，包含以下列：
        - date: 日期
        - 主力净流入_净占比: 主力资金净流入占比
        - 超大单净流入_净占比: 超大单资金净流入占比
        - openPrice: 开盘价
        - closePrice: 收盘价
        - highestPrice: 最高价
        - lowestPrice: 最低价
        - chgPct: 涨跌幅
    
    返回:
    signals: 检测到的交易信号列表
    """
    signals = []
    
    if len(data) < 10:  # 至少需要10天数据
        return signals
    
    data = data.reset_index(drop=True)  # 重置索引确保连续

    i = 0
    while i < len(data) - 7:  # 确保有足够的后续数据进行交易
        # 检查是否开始连续负值期
        if (is_negative_value(data.iloc[i]["主力净流入_净占比"]) and
            is_negative_value(data.iloc[i]["超大单净流入_净占比"])):

            # 计算连续负值天数
            consecutive_days = 0
            j = i
            while (j < len(data) and
                   is_negative_value(data.iloc[j]["主力净流入_净占比"]) and
                   is_negative_value(data.iloc[j]["超大单净流入_净占比"])):
                consecutive_days += 1
                j += 1

            # 检查是否满足连续5天或以上的条件
            if consecutive_days >= 5 and j < len(data):
                # 检查转正信号：第N+1天两个指标都转为正值
                if (is_positive_value(data.iloc[j]["主力净流入_净占比"]) and
                    is_positive_value(data.iloc[j]["超大单净流入_净占比"])):

                    # 确保有足够的后续数据进行交易（需要第N+2天买入，第N+3天卖出）
                    if j + 2 < len(data):
                        # 记录交易信号
                        negative_start_date = data.iloc[i]["date"]
                        negative_end_date = data.iloc[j-1]["date"]
                        confirm_date = data.iloc[j]["date"]
                        buy_date = data.iloc[j+1]["date"]
                        sell_date = data.iloc[j+2]["date"]

                        # 获取交易数据
                        buy_day_data = data.iloc[j+1]
                        sell_day_data = data.iloc[j+2]

                        buy_price = buy_day_data["openPrice"]  # 买入价：第N+2天开盘价
                        sell_price = sell_day_data["highestPrice"]  # 卖出价：第N+3天最高价

                        # 计算收益率
                        if buy_price > 0:
                            return_rate = ((sell_price - buy_price) / buy_price) * 100
                        else:
                            return_rate = 0

                        # 记录结果
                        signal = {
                            "连续负值开始日期": negative_start_date,
                            "连续负值结束日期": negative_end_date,
                            "连续负值天数": consecutive_days,
                            "转正确认日期": confirm_date,
                            "买入日期": buy_date,
                            "卖出日期": sell_date,
                            "买入价格_开盘价": buy_price,
                            "买入日期_开盘价": buy_day_data["openPrice"],
                            "买入日期_收盘价": buy_day_data["closePrice"],
                            "买入日期_最低价": buy_day_data["lowestPrice"],
                            "买入日期_涨跌幅": buy_day_data["chgPct"],
                            "卖出价格_最高价": sell_price,
                            "收益率": round(return_rate, 2),
                            "确认日主力净占比": data.iloc[j]["主力净流入_净占比"],
                            "确认日超大单净占比": data.iloc[j]["超大单净流入_净占比"]
                        }

                        signals.append(signal)

                        # 跳过已处理的区间
                        i = j + 3
                    else:
                        i = j
                else:
                    i = j
            else:
                i = j if j > i else i + 1
        else:
            i += 1
    
    return signals

# 创建测试数据
def create_test_data():
    """创建测试数据来验证算法"""
    dates = [f"2025{i:04d}" for i in range(1, 31)]  # 30天的测试数据
    
    # 创建一个包含连续负值转正信号的测试案例
    test_data = []
    
    for i, date in enumerate(dates):
        if i < 6:  # 前6天：连续负值
            主力净占比 = -2.5 - i * 0.5  # 递减的负值
            超大单净占比 = -1.8 - i * 0.3
        elif i == 6:  # 第7天：转正
            主力净占比 = 1.2
            超大单净占比 = 0.8
        elif i == 7:  # 第8天：买入日
            主力净占比 = 0.5
            超大单净占比 = 0.3
        elif i == 8:  # 第9天：卖出日
            主力净占比 = 1.8
            超大单净占比 = 1.1
        else:  # 其他天：随机数据
            主力净占比 = np.random.uniform(-3, 3)
            超大单净占比 = np.random.uniform(-2, 2)
        
        # 模拟价格数据
        base_price = 10.0 + i * 0.1
        open_price = base_price + np.random.uniform(-0.2, 0.2)
        close_price = open_price + np.random.uniform(-0.3, 0.3)
        high_price = max(open_price, close_price) + np.random.uniform(0, 0.2)
        low_price = min(open_price, close_price) - np.random.uniform(0, 0.2)
        chg_pct = ((close_price - base_price) / base_price) * 100
        
        test_data.append({
            "date": date,
            "主力净流入_净占比": 主力净占比,
            "超大单净流入_净占比": 超大单净占比,
            "openPrice": round(open_price, 2),
            "closePrice": round(close_price, 2),
            "highestPrice": round(high_price, 2),
            "lowestPrice": round(low_price, 2),
            "chgPct": round(chg_pct, 2)
        })
    
    return pd.DataFrame(test_data)

# 运行测试
if __name__ == "__main__":
    print("连续负值转正信号算法测试")
    print("="*50)
    
    # 创建测试数据
    test_df = create_test_data()
    print("测试数据创建完成，共{}天数据".format(len(test_df)))
    
    # 显示测试数据的前10天
    print("\n测试数据示例（前10天）:")
    print(test_df[['date', '主力净流入_净占比', '超大单净流入_净占比', 'openPrice', 'closePrice']].head(10))
    
    # 运行算法
    signals = detect_consecutive_negative_to_positive_signals(test_df)
    
    print(f"\n检测结果:")
    print(f"找到 {len(signals)} 个交易信号")
    
    if signals:
        for i, signal in enumerate(signals):
            print(f"\n信号 {i+1}:")
            print(f"  连续负值期: {signal['连续负值开始日期']} 到 {signal['连续负值结束日期']} (共{signal['连续负值天数']}天)")
            print(f"  转正确认: {signal['转正确认日期']}")
            print(f"  买入: {signal['买入日期']} 开盘价 {signal['买入价格_开盘价']}")
            print(f"  卖出: {signal['卖出日期']} 最高价 {signal['卖出价格_最高价']}")
            print(f"  收益率: {signal['收益率']}%")
            print(f"  确认日指标: 主力{signal['确认日主力净占比']}, 超大单{signal['确认日超大单净占比']}")
    
    print("\n算法测试完成！")
