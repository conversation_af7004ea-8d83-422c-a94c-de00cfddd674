#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连续负值转正信号股票回测程序
功能：识别连续5天或以上主力净流入和超大单净流入都为负值，然后转正的股票交易机会
策略：在转正信号确认后第二天买入，第三天卖出
作者：AI Assistant
创建时间：2025-07-28

核心逻辑：
1. 连续负值检测：连续5天或5天以上，'主力净流入-净占比'和'超大单净流入-净占比'都为负值
2. 转正信号确认：连续负值期结束后的下一天（第N+1天），这两个指标都转为正值
3. 交易执行策略：
   - 买入时机：转正信号确认后的第二天（第N+2天）开盘价买入
   - 卖出时机：买入后第二天（第N+3天）最高价卖出
4. 收益计算：计算从买入价到卖出价的收益率

输出字段：
- 股票代码、股票名称
- 连续负值开始日期、结束日期、连续天数
- 转正确认日期、买入日期、卖出日期
- 买入价格（开盘价）、买入日期的开盘价、收盘价、最低价、涨跌幅
- 卖出价格（最高价）、收益率
- 转正确认日的关键指标：涨跌幅、收盘价、开盘价、成交量、成交量比值
"""

import os
import pandas as pd
import csv
from datetime import datetime, timedelta

# 定义目录路径
equd_dir = "d:\\Andy\\Data\\MktEqudGet\\"
limit_dir = "d:\\Andy\\Data\\MktLimitGet\\"
fund_flow_dir = r'stock_fund_flow_data'

# 检查数据目录是否存在
print("检查数据目录...")
required_dirs = [
    ("股票行情数据", equd_dir),
    ("涨跌停数据", limit_dir),
    ("资金流向数据", fund_flow_dir)
]

missing_dirs = []
for name, path in required_dirs:
    if os.path.exists(path):
        print(f"   ✓ {name}: {path}")
    else:
        print(f"   ✗ {name}: {path} (不存在)")
        missing_dirs.append(name)

if missing_dirs:
    print(f"\n错误：缺少必要的数据目录: {', '.join(missing_dirs)}")
    print("请确保以下目录存在并包含数据文件：")
    for name, path in required_dirs:
        print(f"  - {name}: {path}")
    exit(1)

# 定义日期范围（与原程序保持一致）
start_date = "20250122"
end_date = "20250724"
print(f"\nStart date: {start_date}")
print(f"End date: {end_date}")
output_file = f'lianxu_fuzhi_zhuanzheng_huice_{end_date}.csv'
print(f"Output file: {output_file}")

# 辅助函数（复用原程序）
def extract_stock_code(sec_id):
    """从SecID中提取6位股票代码"""
    if not sec_id or not isinstance(sec_id, str):
        return ""
    return sec_id[:6]

def find_matching_fund_flow_file(stock_code, fund_flow_dir):
    """查找匹配的资金流向文件"""
    patterns = [
        f"{stock_code}_sz_fund_flow.csv",  # 深圳交易所
        f"{stock_code}_sh_fund_flow.csv"   # 上海交易所
    ]

    for pattern in patterns:
        file_path = os.path.join(fund_flow_dir, pattern)
        if os.path.exists(file_path):
            return file_path
    return None

def convert_date_to_yyyymmdd(date_str):
    """将YYYY-MM-DD格式转换为YYYYMMDD格式"""
    if not date_str:
        return ""
    try:
        return date_str.replace('-', '')
    except Exception:
        return ""

def is_negative_value(value):
    """判断是否为负值"""
    if pd.isna(value) or value == "" or value is None:
        return False
    try:
        return float(value) < 0
    except (ValueError, TypeError):
        return False

def is_positive_value(value):
    """判断是否为正值"""
    if pd.isna(value) or value == "" or value is None:
        return False
    try:
        return float(value) > 0
    except (ValueError, TypeError):
        return False

# 获取所有交易日的文件名
print("1. 获取交易日列表...")
all_trading_dates = sorted([f.split('.')[0] for f in os.listdir(equd_dir) if f.endswith('.csv')])
# 过滤日期范围内的交易日
trading_dates = [date for date in all_trading_dates if start_date <= date <= end_date]
print(f"   找到 {len(trading_dates)} 个交易日")

# 读取并合并数据
print("2. 读取股票基础数据...")
data = []
for i, date in enumerate(trading_dates):
    if (i + 1) % 10 == 0 or i == 0:
        print(f"   处理进度: {i + 1}/{len(trading_dates)} ({date})")

    equd_file = os.path.join(equd_dir, f"{date}.csv")
    limit_file = os.path.join(limit_dir, f"{date}.csv")

    if not os.path.exists(equd_file) or not os.path.exists(limit_file):
        continue  # 如果当天不是交易日，跳过

    try:
        equd_df = pd.read_csv(equd_file, encoding='gbk')
        limit_df = pd.read_csv(limit_file, encoding='gbk')

        merged_df = pd.merge(equd_df, limit_df, on=["secID", "secShortName"])
        merged_df["date"] = date
        data.append(merged_df)
    except Exception as e:
        print(f"   警告：读取 {date} 数据失败: {str(e)}")
        continue

if not data:
    print("错误：没有读取到任何数据")
    exit(1)

# 合并所有日期的数据
print("3. 合并和过滤数据...")
all_data = pd.concat(data, ignore_index=True)
print(f"   原始数据记录数: {len(all_data)}")

# 过滤不需要的数据（复用原程序的过滤条件）
all_data = all_data[(all_data['ticker_x'] < 700000) &
        ((all_data['exchangeCD_x'] == "XSHE") | (all_data['exchangeCD_x'] == "XSHG")) &
        (~all_data['secShortName'].str.contains('B')) &
        (~all_data['secShortName'].str.contains('ST')) & (all_data['closePrice'] > 3)]

print(f"   过滤后数据记录数: {len(all_data)}")

# 按secID和日期排序
all_data = all_data.sort_values(by=["secID", "date"])
print(f"   涉及股票数量: {all_data['secID'].nunique()}")

# 核心算法：连续负值检测和转正信号识别
print("4. 执行连续负值转正信号检测...")
results = []
total_groups = len(all_data['secID'].unique())

for idx, (secID, group) in enumerate(all_data.groupby("secID")):
    if (idx + 1) % 100 == 0 or idx == 0:
        print(f"   处理进度: {idx + 1}/{total_groups}, secID: {secID}")

    secShortName = group.iloc[0]["secShortName"]
    group = group.reset_index(drop=True)
    stock_code = extract_stock_code(secID)
    
    # 查找资金流向文件
    fund_flow_file = find_matching_fund_flow_file(stock_code, fund_flow_dir)
    if not fund_flow_file:
        continue  # 没有资金流向数据，跳过
    
    try:
        fund_df = pd.read_csv(fund_flow_file, encoding='utf-8-sig')
        # 将资金流向数据转换为字典，以日期为键
        fund_flow_data = {}
        for _, row in fund_df.iterrows():
            date_key = convert_date_to_yyyymmdd(row['日期'])
            if date_key:
                fund_flow_data[date_key] = row
    except Exception as e:
        print(f"   警告：读取资金流向文件失败 {fund_flow_file}: {str(e)}")
        continue
    
    # 为每个交易日添加资金流向数据
    group_with_fund = []
    for _, row in group.iterrows():
        current_date = row["date"]
        if current_date in fund_flow_data:
            fund_row = fund_flow_data[current_date]
            row_dict = row.to_dict()
            row_dict.update({
                "主力净流入_净占比": fund_row.get("主力净流入-净占比", ""),
                "超大单净流入_净占比": fund_row.get("超大单净流入-净占比", "")
            })
            group_with_fund.append(row_dict)

    if len(group_with_fund) < 10:  # 至少需要10天数据才能进行分析
        continue

    group_with_fund = pd.DataFrame(group_with_fund)
    group_with_fund = group_with_fund.reset_index(drop=True)  # 重置索引确保连续
    
    # 连续负值检测算法
    i = 0
    while i < len(group_with_fund) - 7:  # 确保有足够的后续数据进行交易
        # 检查是否开始连续负值期
        if (is_negative_value(group_with_fund.iloc[i]["主力净流入_净占比"]) and
            is_negative_value(group_with_fund.iloc[i]["超大单净流入_净占比"])):

            # 计算连续负值天数
            consecutive_days = 0
            j = i
            while (j < len(group_with_fund) and
                   is_negative_value(group_with_fund.iloc[j]["主力净流入_净占比"]) and
                   is_negative_value(group_with_fund.iloc[j]["超大单净流入_净占比"])):
                consecutive_days += 1
                j += 1

            # 检查是否满足连续5天或以上的条件
            if consecutive_days >= 5 and j < len(group_with_fund):
                # 检查转正信号：第N+1天两个指标都转为正值
                if (is_positive_value(group_with_fund.iloc[j]["主力净流入_净占比"]) and
                    is_positive_value(group_with_fund.iloc[j]["超大单净流入_净占比"])):

                    # 确保有足够的后续数据进行交易（需要第N+2天买入，第N+3天卖出）
                    if j + 2 < len(group_with_fund):
                        # 记录交易信号
                        negative_start_date = group_with_fund.iloc[i]["date"]
                        negative_end_date = group_with_fund.iloc[j-1]["date"]
                        confirm_date = group_with_fund.iloc[j]["date"]
                        buy_date = group_with_fund.iloc[j+1]["date"]
                        sell_date = group_with_fund.iloc[j+2]["date"]

                        # 获取交易数据
                        confirm_day_data = group_with_fund.iloc[j]  # 转正确认日数据
                        prev_confirm_day_data = group_with_fund.iloc[j-1]  # 转正确认日前一天数据
                        buy_day_data = group_with_fund.iloc[j+1]  # 买入日数据
                        sell_day_data = group_with_fund.iloc[j+2]  # 卖出日数据

                        # 计算成交量比值
                        confirm_day_vol = confirm_day_data.get("turnoverVol", 0)
                        prev_confirm_day_vol = prev_confirm_day_data.get("turnoverVol", 0)
                        vol_ratio = 0
                        if prev_confirm_day_vol and prev_confirm_day_vol > 0:
                            vol_ratio = confirm_day_vol / prev_confirm_day_vol

                        buy_price = buy_day_data["openPrice"]+buy_day_data["preClosePrice"]-buy_day_data["actPreClosePrice"]  # 买入价：第N+2天开盘价
                        sell_price = sell_day_data["highestPrice"]  # 卖出价：第N+3天最高价

                        # 计算收益率
                        if buy_price > 0:
                            return_rate = ((sell_price - buy_price) / buy_price) * 100
                        else:
                            return_rate = 0

                        # 记录结果
                        record = {
                            "secID": secID,
                            "secShortName": secShortName,
                            "连续负值开始日期": negative_start_date,
                            "连续负值结束日期": negative_end_date,
                            "连续负值天数": consecutive_days,
                            "转正确认日期": confirm_date,
                            "买入日期": buy_date,
                            "卖出日期": sell_date,
                            "买入价格_开盘价": buy_price,
                            "买入日期_开盘价": buy_day_data["openPrice"],
                            "买入日期_收盘价": buy_day_data["closePrice"],
                            "买入日期_最低价": buy_day_data["lowestPrice"],
                            "买入日期_涨跌幅": buy_day_data["chgPct"],
                            "卖出价格_最高价": sell_price,
                            "收益率": round(return_rate, 2),
                            "确认日主力净占比": confirm_day_data["主力净流入_净占比"],
                            "确认日超大单净占比": confirm_day_data["超大单净流入_净占比"],
                            # 新增转正确认日的关键指标
                            "转正日_涨跌幅": confirm_day_data.get("chgPct", ""),
                            "转正日_收盘价": confirm_day_data.get("closePrice", ""),
                            "转正日_开盘价": confirm_day_data.get("openPrice", ""),
                            "转正日_成交量": confirm_day_vol,
                            "转正前一天_成交量": prev_confirm_day_vol,
                            "转正日成交量比值": round(vol_ratio, 2) if vol_ratio > 0 else ""
                        }

                        results.append(record)
                        print(f"   发现信号: {secID} ({secShortName}) - 连续{consecutive_days}天负值，{confirm_date}转正，收益率{return_rate:.2f}%")

                        # 跳过已处理的区间
                        i = j + 3
                    else:
                        i = j
                else:
                    i = j
            else:
                i = j if j > i else i + 1
        else:
            i += 1

# 保存结果
print("5. 保存分析结果...")
if not results:
    print("   警告：没有找到符合条件的交易信号")
    exit(1)

# 转换结果为DataFrame
results_df = pd.DataFrame(results)
print(f"   找到 {len(results_df)} 个交易信号")

# 按买入日期和股票代码排序
results_df = results_df.sort_values(by=['买入日期', 'secID'])

# 保存结果到CSV文件
try:
    results_df.to_csv(output_file, index=False, encoding='gbk')
    print(f"   ✓ 结果已保存到: {output_file}")
except Exception as e:
    print(f"   错误：保存文件失败: {str(e)}")
    # 尝试使用utf-8编码保存
    try:
        output_file_utf8 = output_file.replace('.csv', '_utf8.csv')
        results_df.to_csv(output_file_utf8, index=False, encoding='utf-8-sig')
        print(f"   ✓ 已使用UTF-8编码保存到: {output_file_utf8}")
    except Exception as e2:
        print(f"   错误：UTF-8保存也失败: {str(e2)}")

# 显示统计信息
print("\n6. 数据统计报告")
print("="*80)
print(f"分析日期范围: {start_date} 到 {end_date}")
print(f"总交易日数: {len(trading_dates)}")
print(f"找到交易信号数量: {len(results_df)}")
print(f"策略说明: 连续5天以上负值转正信号交易策略")

# 收益率统计
if len(results_df) > 0:
    avg_return = results_df['收益率'].mean()
    max_return = results_df['收益率'].max()
    min_return = results_df['收益率'].min()
    positive_count = len(results_df[results_df['收益率'] > 0])
    negative_count = len(results_df[results_df['收益率'] < 0])
    win_rate = (positive_count / len(results_df)) * 100 if len(results_df) > 0 else 0

    print(f"\n收益率统计:")
    print(f"  平均收益率: {avg_return:.2f}%")
    print(f"  最高收益率: {max_return:.2f}%")
    print(f"  最低收益率: {min_return:.2f}%")
    print(f"  盈利次数: {positive_count}")
    print(f"  亏损次数: {negative_count}")
    print(f"  胜率: {win_rate:.2f}%")

# 连续负值天数统计
consecutive_days_stats = results_df.groupby('连续负值天数').size()
print(f"\n连续负值天数分布:")
for days, count in consecutive_days_stats.items():
    print(f"  {days}天: {count}次")

# 按月份统计
results_df['买入月份'] = results_df['买入日期'].str[:6]
monthly_stats = results_df.groupby('买入月份').agg({
    'secID': 'count',
    '收益率': 'mean'
}).round(2)
monthly_stats.columns = ['信号数量', '平均收益率']
print(f"\n按月份统计:")
for month, row in monthly_stats.iterrows():
    print(f"  {month}: {row['信号数量']}个信号, 平均收益率{row['平均收益率']}%")

# 转正日指标统计
if len(results_df) > 0:
    # 过滤有效的转正日涨跌幅数据
    valid_chg_data = results_df[results_df['转正日_涨跌幅'] != '']['转正日_涨跌幅'].astype(float)
    valid_vol_ratio_data = results_df[results_df['转正日成交量比值'] != '']['转正日成交量比值'].astype(float)

    if len(valid_chg_data) > 0:
        avg_chg = valid_chg_data.mean()
        positive_chg_count = len(valid_chg_data[valid_chg_data > 0])
        print(f"\n转正日涨跌幅统计:")
        print(f"  平均涨跌幅: {avg_chg:.2f}%")
        print(f"  上涨次数: {positive_chg_count}/{len(valid_chg_data)} ({positive_chg_count/len(valid_chg_data)*100:.1f}%)")

    if len(valid_vol_ratio_data) > 0:
        avg_vol_ratio = valid_vol_ratio_data.mean()
        high_vol_count = len(valid_vol_ratio_data[valid_vol_ratio_data > 1.5])
        print(f"\n转正日成交量比值统计:")
        print(f"  平均成交量比值: {avg_vol_ratio:.2f}")
        print(f"  放量1.5倍以上次数: {high_vol_count}/{len(valid_vol_ratio_data)} ({high_vol_count/len(valid_vol_ratio_data)*100:.1f}%)")

# 显示最佳和最差案例
print(f"\n最佳收益案例（前5个）:")
best_cases = results_df.nlargest(5, '收益率')
for i, (_, row) in enumerate(best_cases.iterrows()):
    print(f"  {i+1}. {row['secID']} ({row['secShortName']}) - {row['买入日期']} - 收益率{row['收益率']}%")

print(f"\n最差收益案例（前5个）:")
worst_cases = results_df.nsmallest(5, '收益率')
for i, (_, row) in enumerate(worst_cases.iterrows()):
    print(f"  {i+1}. {row['secID']} ({row['secShortName']}) - {row['买入日期']} - 收益率{row['收益率']}%")

# 显示数据示例
print(f"\n交易信号示例（前3个）:")
print("-" * 80)
if len(results_df) > 0:
    for i, (_, row) in enumerate(results_df.head(3).iterrows()):
        print(f"\n第 {i+1} 个信号:")
        print(f"  股票: {row['secID']} ({row['secShortName']})")
        print(f"  连续负值期: {row['连续负值开始日期']} 到 {row['连续负值结束日期']} (共{row['连续负值天数']}天)")
        print(f"  转正确认: {row['转正确认日期']}")
        print(f"  买入: {row['买入日期']} 开盘价 {row['买入价格_开盘价']}")
        print(f"  买入日数据: 开盘{row['买入日期_开盘价']}, 收盘{row['买入日期_收盘价']}, 最低{row['买入日期_最低价']}, 涨跌幅{row['买入日期_涨跌幅']}%")
        print(f"  卖出: {row['卖出日期']} 最高价 {row['卖出价格_最高价']}")
        print(f"  收益率: {row['收益率']}%")
        print(f"  确认日指标: 主力净占比{row['确认日主力净占比']}, 超大单净占比{row['确认日超大单净占比']}")
        print(f"  转正日数据: 涨跌幅{row['转正日_涨跌幅']}%, 开盘{row['转正日_开盘价']}, 收盘{row['转正日_收盘价']}")
        print(f"  转正日成交量: {row['转正日_成交量']}, 成交量比值: {row['转正日成交量比值']}")

print("="*80)
print("程序执行完成！")

# 执行完成提示
print("\n正在执行完成提示...")
try:
    import subprocess
    subprocess.run([
        "powershell", "-Command",
        "Add-Type -AssemblyName PresentationFramework; [System.Windows.MessageBox]::Show('搞完了', 'Augment')"
    ], check=False)
except Exception:
    pass  # 如果执行失败，忽略错误
