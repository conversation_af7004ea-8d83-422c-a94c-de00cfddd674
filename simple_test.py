#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的算法逻辑验证脚本
"""

print("连续负值转正信号算法验证")
print("="*40)

# 模拟数据：连续6天负值，第7天转正
test_data = [
    {"day": 1, "主力": -2.5, "超大单": -1.8, "开盘": 10.0, "最高": 10.2},
    {"day": 2, "主力": -3.0, "超大单": -2.1, "开盘": 9.8, "最高": 10.0},
    {"day": 3, "主力": -2.8, "超大单": -1.9, "开盘": 9.9, "最高": 10.1},
    {"day": 4, "主力": -3.2, "超大单": -2.3, "开盘": 9.7, "最高": 9.9},
    {"day": 5, "主力": -2.9, "超大单": -2.0, "开盘": 9.8, "最高": 10.0},
    {"day": 6, "主力": -2.1, "超大单": -1.5, "开盘": 9.9, "最高": 10.1},
    {"day": 7, "主力": 1.2, "超大单": 0.8, "开盘": 10.1, "最高": 10.3},  # 转正日
    {"day": 8, "主力": 0.5, "超大单": 0.3, "开盘": 10.2, "最高": 10.5},  # 买入日
    {"day": 9, "主力": 1.8, "超大单": 1.1, "开盘": 10.3, "最高": 10.8},  # 卖出日
]

print("测试数据:")
for data in test_data:
    print(f"第{data['day']}天: 主力{data['主力']:5.1f}, 超大单{data['超大单']:5.1f}, 开盘{data['开盘']:5.1f}, 最高{data['最高']:5.1f}")

# 检测连续负值
print("\n算法检测过程:")
consecutive_days = 0
for i, data in enumerate(test_data):
    if data["主力"] < 0 and data["超大单"] < 0:
        consecutive_days += 1
        print(f"第{data['day']}天: 连续负值第{consecutive_days}天")
    else:
        if consecutive_days >= 5:
            print(f"第{data['day']}天: 转正信号确认！连续负值{consecutive_days}天结束")
            if i + 2 < len(test_data):
                buy_day = test_data[i + 1]
                sell_day = test_data[i + 2]
                buy_price = buy_day["开盘"]
                sell_price = sell_day["最高"]
                return_rate = ((sell_price - buy_price) / buy_price) * 100
                
                print(f"买入: 第{buy_day['day']}天开盘价 {buy_price}")
                print(f"卖出: 第{sell_day['day']}天最高价 {sell_price}")
                print(f"收益率: {return_rate:.2f}%")
            break
        else:
            consecutive_days = 0

print("\n算法验证完成！")
